import 'dart:async';
import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:emartdriver/constants.dart';
import 'package:emartdriver/main.dart';
import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/services/notification_service.dart';
import 'package:emartdriver/vendor_status_enum.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_foreground_task/flutter_foreground_task.dart';

/// StatusTaskHandler para monitorar pedidos em segundo plano
/// Utiliza flutter_foreground_task para manter o monitoramento ativo
/// mesmo quando o app está em background
class StatusTaskHandler extends TaskHandler {
  static const String _taskName = 'StatusTaskHandler';
  static const int _updateInterval = 10; // 10 segundos conforme preferência

  StreamSubscription<QuerySnapshot>? _ordersSubscription;
  final Set<String> _processedOrderIds = {};
  String? _currentUserId;

  @override
  Future<void> onStart(DateTime timestamp, TaskStarter starter) async {
    log('StatusTaskHandler iniciado em: $timestamp');
    _currentUserId = FirebaseAuth.instance.currentUser?.uid;

    if (_currentUserId != null) {
      _startOrderMonitoring();
    } else {
      log('Usuário não autenticado - parando task handler');
      FlutterForegroundTask.stopService();
    }
  }

  @override
  void onRepeatEvent(DateTime timestamp) {
    // Atualiza a notificação do foreground service a cada 10 segundos
    _updateForegroundNotification();
  }

  @override
  Future<void> onDestroy(DateTime timestamp, bool isTaskRemoved) async {
    log('StatusTaskHandler destruído em: $timestamp');
    _ordersSubscription?.cancel();
  }

  /// Inicia o monitoramento de novos pedidos
  void _startOrderMonitoring() {
    log('Iniciando monitoramento de pedidos para usuário: $_currentUserId');

    try {
      // Monitora pedidos com status 'driverSearching' (procurando entregador)
      final query = FirebaseFirestore.instance
          .collection(ORDERS)
          .where('status', isEqualTo: OrderStatus.driverSearching.description);

      _ordersSubscription = query.snapshots().listen(
        _handleOrdersUpdate,
        onError: (error) {
          log('Erro no monitoramento de pedidos: $error');
        },
      );
    } catch (e) {
      log('Erro ao iniciar monitoramento: $e');
    }
  }

  /// Processa atualizações de pedidos
  void _handleOrdersUpdate(QuerySnapshot querySnapshot) {
    try {
      for (var change in querySnapshot.docChanges) {
        if (change.type == DocumentChangeType.added) {
          final data = change.doc.data() as Map<String, dynamic>;
          final order = OrderModel.fromJson(data);

          // Verifica se é um novo pedido que ainda não foi processado
          if (!_processedOrderIds.contains(order.id)) {
            _processedOrderIds.add(order.id);
            _handleNewOrder(order);
          }
        }
      }
    } catch (e) {
      log('Erro ao processar atualizações de pedidos: $e');
    }
  }

  /// Processa um novo pedido
  void _handleNewOrder(OrderModel order) {
    log('Novo pedido detectado: ${order.id} - Loja: ${order.vendor.title}');

    // Verifica se o app está em background
    if (!_isAppInForeground()) {
      _sendNewOrderNotification(order);
    }

    // Atualiza estatísticas do foreground service
    _updateForegroundNotification();
  }

  /// Verifica se o app está em primeiro plano
  bool _isAppInForeground() {
    // Verifica se o contexto global está disponível
    return globalContext != null;
  }

  /// Envia notificação para novo pedido
  void _sendNewOrderNotification(OrderModel order) {
    try {
      NotificationService.showNewOrderNotification(
        orderId: order.id,
        storeName: order.vendor.title,
        storeAddress: _getStoreAddress(order),
      );

      log('Notificação enviada para pedido: ${order.id}');
    } catch (e) {
      log('Erro ao enviar notificação: $e');
    }
  }

  /// Obtém endereço da loja formatado
  String _getStoreAddress(OrderModel order) {
    final address = order.vendor.address_store;
    if (address == null) return 'Endereço não disponível';

    return '${address.logradouro ?? ''}, ${address.numero ?? ''} - ${address.bairro ?? ''}';
  }

  /// Atualiza a notificação do foreground service
  void _updateForegroundNotification() {
    final now = DateTime.now();
    final timeString =
        '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';

    FlutterForegroundTask.updateService(
      notificationTitle: 'Tá Entregue - Monitorando Pedidos',
      notificationText:
          'Última atualização: $timeString\nPedidos processados: ${_processedOrderIds.length}',
    );
  }

  /// Limpa cache de pedidos processados (evita acúmulo excessivo)
  void _clearOldProcessedOrders() {
    if (_processedOrderIds.length > 100) {
      final oldIds = _processedOrderIds.take(50).toList();
      for (final id in oldIds) {
        _processedOrderIds.remove(id);
      }
      log('Cache de pedidos processados limpo. Mantidos: ${_processedOrderIds.length}');
    }
  }
}

/// Classe para gerenciar o StatusTaskHandler
class StatusTaskManager {
  static bool _isInitialized = false;
  static bool _isRunning = false;

  /// Inicializa o foreground task
  static Future<void> initialize() async {
    if (_isInitialized) return;

    FlutterForegroundTask.init(
      androidNotificationOptions: AndroidNotificationOptions(
        channelId: 'status_task_channel',
        channelName: 'Monitoramento de Pedidos',
        channelDescription: 'Monitora novos pedidos em segundo plano',
        channelImportance: NotificationChannelImportance.LOW,
        priority: NotificationPriority.LOW,
      ),
      iosNotificationOptions: const IOSNotificationOptions(
        showNotification: true,
        playSound: false,
      ),
      foregroundTaskOptions: ForegroundTaskOptions(
        eventAction: ForegroundTaskEventAction.repeat(10000), // 10 segundos
        autoRunOnBoot: false,
        autoRunOnMyPackageReplaced: false,
        allowWakeLock: true,
        allowWifiLock: true,
      ),
    );

    _isInitialized = true;
    log('StatusTaskManager inicializado');
  }

  /// Inicia o monitoramento de pedidos
  static Future<bool> startMonitoring() async {
    if (!_isInitialized) {
      await initialize();
    }

    if (_isRunning) {
      log('Monitoramento já está em execução');
      return true;
    }

    try {
      final success = await FlutterForegroundTask.startService(
        serviceId: 256,
        notificationTitle: 'Tá Entregue - Monitorando Pedidos',
        notificationText: 'Procurando novos pedidos...',
        callback: _startCallback,
      );

      final isSuccess = success is ServiceRequestSuccess;
      if (isSuccess) {
        _isRunning = true;
        log('Monitoramento de pedidos iniciado com sucesso');
      } else {
        log('Falha ao iniciar monitoramento de pedidos');
      }

      return isSuccess;
    } catch (e) {
      log('Erro ao iniciar monitoramento: $e');
      return false;
    }
  }

  /// Para o monitoramento de pedidos
  static Future<bool> stopMonitoring() async {
    if (!_isRunning) {
      log('Monitoramento não está em execução');
      return true;
    }

    try {
      final success = await FlutterForegroundTask.stopService();

      final isSuccess = success is ServiceRequestSuccess;
      if (isSuccess) {
        _isRunning = false;
        log('Monitoramento de pedidos parado com sucesso');
      } else {
        log('Falha ao parar monitoramento de pedidos');
      }

      return isSuccess;
    } catch (e) {
      log('Erro ao parar monitoramento: $e');
      return false;
    }
  }

  /// Verifica se o monitoramento está ativo
  static bool get isRunning => _isRunning;

  /// Callback para iniciar o task handler
  @pragma('vm:entry-point')
  static void _startCallback() {
    FlutterForegroundTask.setTaskHandler(StatusTaskHandler());
  }

  /// Processa ações dos botões da notificação
  static void handleNotificationAction(String action) {
    switch (action) {
      case 'stop_monitoring':
        stopMonitoring();
        break;
      case 'update_location':
        // Força uma atualização
        log('Atualização forçada solicitada');
        break;
      default:
        log('Ação desconhecida: $action');
    }
  }
}
