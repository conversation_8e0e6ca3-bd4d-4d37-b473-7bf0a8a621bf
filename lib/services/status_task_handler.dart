import 'dart:async';
import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:emartdriver/constants.dart';
import 'package:emartdriver/main.dart';
import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/services/notification_service.dart';
import 'package:emartdriver/vendor_status_enum.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_foreground_task/flutter_foreground_task.dart';

/// StatusTaskHandler para monitorar pedidos em segundo plano
/// Utiliza flutter_foreground_task para manter o monitoramento ativo
/// mesmo quando o app está em background
class StatusTaskHandler extends TaskHandler {
  static const String _taskName = 'StatusTaskHandler';
  static const int _updateInterval = 10; // 10 segundos conforme preferência

  StreamSubscription<QuerySnapshot>? _ordersSubscription;
  final Set<String> _processedOrderIds = {};
  String? _currentUserId;

  @override
  Future<void> onStart(DateTime timestamp, TaskStarter starter) async {
    log('🚀 ===== STATUS TASK HANDLER INICIADO =====');
    log('⏰ Timestamp: $timestamp');
    log('🔧 Starter: ${starter.name}');

    _currentUserId = FirebaseAuth.instance.currentUser?.uid;
    log('👤 Usuário atual: $_currentUserId');

    if (_currentUserId != null) {
      log('✅ Usuário autenticado - iniciando monitoramento');
      _startOrderMonitoring();
    } else {
      log('❌ Usuário não autenticado - parando task handler');
      FlutterForegroundTask.stopService();
    }
  }

  @override
  void onRepeatEvent(DateTime timestamp) {
    // Atualiza a notificação do foreground service a cada 10 segundos
    _updateForegroundNotification();
  }

  @override
  Future<void> onDestroy(DateTime timestamp, bool isTaskRemoved) async {
    log('🛑 ===== STATUS TASK HANDLER DESTRUÍDO =====');
    log('⏰ Timestamp: $timestamp');
    log('🗑️ Task removida: $isTaskRemoved');
    log('📊 Total de pedidos processados: ${_processedOrderIds.length}');

    _ordersSubscription?.cancel();
    log('✅ Subscription cancelada');
  }

  /// Inicia o monitoramento de novos pedidos
  void _startOrderMonitoring() {
    log('🚀 ===== INICIANDO MONITORAMENTO DE PEDIDOS =====');
    log('👤 Usuário ID: $_currentUserId');
    log('📊 Status monitorado: ${OrderStatus.driverSearching.description}');
    log('🔄 Intervalo de atualização: $_updateInterval segundos');

    try {
      // Monitora pedidos com status 'driverSearching' (procurando entregador)
      final query = FirebaseFirestore.instance
          .collection(ORDERS)
          .where('status', isEqualTo: 'Procurando entregador');

      log('🔗 Configurando listener do Firestore...');
      _ordersSubscription = query.snapshots().listen(
        _handleOrdersUpdate,
        onError: (error) {
          log('❌ Erro no monitoramento de pedidos: $error');
        },
      );

      log('✅ Listener configurado com sucesso');
      log('🎯 Monitoramento ativo - aguardando novos pedidos...');
    } catch (e) {
      log('❌ Erro ao iniciar monitoramento: $e');
    }
  }

  /// Processa atualizações de pedidos
  void _handleOrdersUpdate(QuerySnapshot querySnapshot) {
    try {
      log('📡 Recebida atualização do Firestore');
      log('📊 Total de mudanças: ${querySnapshot.docChanges.length}');

      for (var change in querySnapshot.docChanges) {
        log('🔄 Tipo de mudança: ${change.type.toString()}');

        if (change.type == DocumentChangeType.added) {
          final data = change.doc.data() as Map<String, dynamic>;
          final order = OrderModel.fromJson(data);

          log('📋 Pedido encontrado: ${order.id}');

          // Verifica se é um novo pedido que ainda não foi processado
          if (!_processedOrderIds.contains(order.id)) {
            log('🆕 Pedido ${order.id} é novo - processando...');
            _processedOrderIds.add(order.id);
            _handleNewOrder(order);
          } else {
            log('🔄 Pedido ${order.id} já foi processado anteriormente');
          }
        }
      }
    } catch (e) {
      log('❌ Erro ao processar atualizações de pedidos: $e');
    }
  }

  /// Processa um novo pedido
  void _handleNewOrder(OrderModel order) {
    log('🆕 ===== NOVO PEDIDO DETECTADO =====');
    log('📋 ID do Pedido: ${order.id}');
    log('🏪 Loja: ${order.vendor.title}');
    log('📍 Endereço da Loja: ${_getStoreAddress(order)}');
    log('💰 Taxa de Entrega: R\$ ${order.deliveryCharge ?? "0.00"}');
    log('📊 Status: ${order.status.toString()}');
    log('⏰ Criado em: ${order.createdAt.toString()}');
    log('🔄 Total de pedidos processados: ${_processedOrderIds.length}');

    // Verifica se o app está em background
    final isAppInBackground = !_isAppInForeground();
    log('📱 App em background: $isAppInBackground');

    if (isAppInBackground) {
      log('🔔 Enviando notificação push...');
      _sendNewOrderNotification(order);
    } else {
      log('📱 App em primeiro plano - notificação não enviada');
    }

    // Atualiza estatísticas do foreground service
    _updateForegroundNotification();
    log('✅ Processamento do pedido ${order.id} concluído');
  }

  /// Verifica se o app está em primeiro plano
  bool _isAppInForeground() {
    // Verifica se o contexto global está disponível
    return globalContext != null;
  }

  /// Envia notificação para novo pedido
  void _sendNewOrderNotification(OrderModel order) {
    try {
      log('🔔 ===== ENVIANDO NOTIFICAÇÃO =====');
      log('📋 Pedido: ${order.id}');
      log('🏪 Loja: ${order.vendor.title}');
      log('📍 Endereço: ${_getStoreAddress(order)}');

      NotificationService.showNewOrderNotification(
        orderId: order.id,
        storeName: order.vendor.title,
        storeAddress: _getStoreAddress(order),
      );

      log('✅ Notificação enviada com sucesso para pedido: ${order.id}');
      log('🔊 Notificação deve incluir som e vibração');
    } catch (e) {
      log('❌ Erro ao enviar notificação para pedido ${order.id}: $e');
    }
  }

  /// Obtém endereço da loja formatado
  String _getStoreAddress(OrderModel order) {
    final address = order.vendor.address_store;
    if (address == null) return 'Endereço não disponível';

    return '${address.logradouro ?? ''}, ${address.numero ?? ''} - ${address.bairro ?? ''}';
  }

  /// Atualiza a notificação do foreground service
  void _updateForegroundNotification() {
    final now = DateTime.now();
    final timeString =
        '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';

    FlutterForegroundTask.updateService(
      notificationTitle: 'Tá Entregue - Monitorando Pedidos',
      notificationText:
          'Última atualização: $timeString\nPedidos processados: ${_processedOrderIds.length}',
    );
  }

  /// Limpa cache de pedidos processados (evita acúmulo excessivo)
  void _clearOldProcessedOrders() {
    if (_processedOrderIds.length > 100) {
      final oldIds = _processedOrderIds.take(50).toList();
      for (final id in oldIds) {
        _processedOrderIds.remove(id);
      }
      log('Cache de pedidos processados limpo. Mantidos: ${_processedOrderIds.length}');
    }
  }
}

/// Classe para gerenciar o StatusTaskHandler
class StatusTaskManager {
  static bool _isInitialized = false;
  static bool _isRunning = false;

  /// Inicializa o foreground task
  static Future<void> initialize() async {
    if (_isInitialized) return;

    FlutterForegroundTask.init(
      androidNotificationOptions: AndroidNotificationOptions(
        channelId: 'status_task_channel',
        channelName: 'Monitoramento de Pedidos',
        channelDescription: 'Monitora novos pedidos em segundo plano',
        channelImportance: NotificationChannelImportance.LOW,
        priority: NotificationPriority.LOW,
      ),
      iosNotificationOptions: const IOSNotificationOptions(
        showNotification: true,
        playSound: false,
      ),
      foregroundTaskOptions: ForegroundTaskOptions(
        eventAction: ForegroundTaskEventAction.repeat(10000), // 10 segundos
        autoRunOnBoot: false,
        autoRunOnMyPackageReplaced: false,
        allowWakeLock: true,
        allowWifiLock: true,
      ),
    );

    _isInitialized = true;
    log('StatusTaskManager inicializado');
  }

  /// Inicia o monitoramento de pedidos
  static Future<bool> startMonitoring() async {
    if (!_isInitialized) {
      await initialize();
    }

    if (_isRunning) {
      log('Monitoramento já está em execução');
      return true;
    }

    try {
      final success = await FlutterForegroundTask.startService(
        serviceId: 256,
        notificationTitle: 'Tá Entregue - Monitorando Pedidos',
        notificationText: 'Procurando novos pedidos...',
        callback: _startCallback,
      );

      final isSuccess = success is ServiceRequestSuccess;
      if (isSuccess) {
        _isRunning = true;
        log('Monitoramento de pedidos iniciado com sucesso');
      } else {
        log('Falha ao iniciar monitoramento de pedidos');
      }

      return isSuccess;
    } catch (e) {
      log('Erro ao iniciar monitoramento: $e');
      return false;
    }
  }

  /// Para o monitoramento de pedidos
  static Future<bool> stopMonitoring() async {
    if (!_isRunning) {
      log('Monitoramento não está em execução');
      return true;
    }

    try {
      final success = await FlutterForegroundTask.stopService();

      final isSuccess = success is ServiceRequestSuccess;
      if (isSuccess) {
        _isRunning = false;
        log('Monitoramento de pedidos parado com sucesso');
      } else {
        log('Falha ao parar monitoramento de pedidos');
      }

      return isSuccess;
    } catch (e) {
      log('Erro ao parar monitoramento: $e');
      return false;
    }
  }

  /// Verifica se o monitoramento está ativo
  static bool get isRunning => _isRunning;

  /// Callback para iniciar o task handler
  @pragma('vm:entry-point')
  static void _startCallback() {
    FlutterForegroundTask.setTaskHandler(StatusTaskHandler());
  }

  /// Processa ações dos botões da notificação
  static void handleNotificationAction(String action) {
    switch (action) {
      case 'stop_monitoring':
        stopMonitoring();
        break;
      case 'update_location':
        // Força uma atualização
        log('Atualização forçada solicitada');
        break;
      default:
        log('Ação desconhecida: $action');
    }
  }
}
