import 'dart:developer';

import 'package:emartdriver/services/notification_service.dart';
import 'package:emartdriver/services/order_monitoring_task_handler.dart';
import 'package:flutter_foreground_task/flutter_foreground_task.dart';

/// Serviço para inicializar todos os componentes necessários do app
class AppInitializationService {
  static bool _isInitialized = false;

  /// Inicializa todos os serviços necessários para o funcionamento do app
  static Future<void> initialize() async {
    if (_isInitialized) {
      log('AppInitializationService já foi inicializado');
      return;
    }

    try {
      log('Iniciando inicialização dos serviços do app...');

      // 1. Inicializa o serviço de notificações
      await _initializeNotificationService();

      // 2. Inicializa o StatusTaskManager
      await _initializeStatusTaskManager();

      // 3. Configura permissões do foreground task
      await _requestForegroundTaskPermissions();

      _isInitialized = true;
      log('Todos os serviços foram inicializados com sucesso');
    } catch (e) {
      log('Erro durante a inicialização dos serviços: $e');
      rethrow;
    }
  }

  /// Inicializa o serviço de notificações
  static Future<void> _initializeNotificationService() async {
    try {
      log('Inicializando NotificationService...');

      // Inicializa o serviço de notificações existente (Firebase)
      final notificationService = NotificationService();
      await notificationService.initInfo();

      // Inicializa as notificações locais para novos pedidos
      await NotificationService.initializeLocalNotifications();

      log('NotificationService inicializado com sucesso');
    } catch (e) {
      log('Erro ao inicializar NotificationService: $e');
      rethrow;
    }
  }

  /// Inicializa o StatusTaskManager
  static Future<void> _initializeStatusTaskManager() async {
    try {
      log('Inicializando OrderMonitoringTaskManager...');

      await OrderMonitoringTaskManager.initialize();

      log('OrderMonitoringTaskManager inicializado com sucesso');
    } catch (e) {
      log('Erro ao inicializar StatusTaskManager: $e');
      rethrow;
    }
  }

  /// Solicita permissões necessárias para o foreground task
  static Future<void> _requestForegroundTaskPermissions() async {
    try {
      log('Solicitando permissões para foreground task...');

      // Solicita permissão para ignorar otimizações de bateria
      final batteryOptimizationStatus =
          await FlutterForegroundTask.isIgnoringBatteryOptimizations;

      if (!batteryOptimizationStatus) {
        log('Solicitando permissão para ignorar otimizações de bateria...');
        await FlutterForegroundTask.requestIgnoreBatteryOptimization();
      }

      // Verifica se as notificações estão habilitadas
      final notificationPermission =
          await FlutterForegroundTask.checkNotificationPermission();
      if (notificationPermission != NotificationPermission.granted) {
        log('Notificações não estão habilitadas. Usuário deve habilitar manualmente.');
      }

      log('Permissões verificadas com sucesso');
    } catch (e) {
      log('Erro ao verificar permissões: $e');
      // Não relança o erro pois as permissões podem ser opcionais
    }
  }

  /// Inicia o monitoramento de pedidos em background
  static Future<bool> startOrderMonitoring() async {
    if (!_isInitialized) {
      log('AppInitializationService não foi inicializado. Inicializando agora...');
      await initialize();
    }

    try {
      log('Iniciando monitoramento de pedidos...');

      final success = await OrderMonitoringTaskManager.startMonitoring();

      if (success) {
        log('Monitoramento de pedidos iniciado com sucesso');
      } else {
        log('Falha ao iniciar monitoramento de pedidos');
      }

      return success;
    } catch (e) {
      log('Erro ao iniciar monitoramento de pedidos: $e');
      return false;
    }
  }

  /// Para o monitoramento de pedidos em background
  static Future<bool> stopOrderMonitoring() async {
    try {
      log('Parando monitoramento de pedidos...');

      final success = await OrderMonitoringTaskManager.stopMonitoring();

      if (success) {
        log('Monitoramento de pedidos parado com sucesso');
      } else {
        log('Falha ao parar monitoramento de pedidos');
      }

      return success;
    } catch (e) {
      log('Erro ao parar monitoramento de pedidos: $e');
      return false;
    }
  }

  /// Verifica se o monitoramento está ativo
  static Future<bool> get isMonitoringActive =>
      FlutterForegroundTask.isRunningService;

  /// Verifica se o serviço foi inicializado
  static bool get isInitialized => _isInitialized;

  /// Obtém informações de status dos serviços
  static Future<Map<String, dynamic>> getServicesStatus() async {
    return {
      'isInitialized': _isInitialized,
      'isMonitoringActive': await FlutterForegroundTask.isRunningService,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Reinicia todos os serviços
  static Future<void> restart() async {
    log('Reiniciando serviços...');

    try {
      // Para o monitoramento se estiver ativo
      if (await FlutterForegroundTask.isRunningService) {
        await stopOrderMonitoring();
      }

      // Marca como não inicializado para forçar reinicialização
      _isInitialized = false;

      // Reinicializa tudo
      await initialize();

      log('Serviços reiniciados com sucesso');
    } catch (e) {
      log('Erro ao reiniciar serviços: $e');
      rethrow;
    }
  }

  /// Limpa recursos e para todos os serviços
  static Future<void> dispose() async {
    log('Fazendo dispose dos serviços...');

    try {
      // Para o monitoramento
      await stopOrderMonitoring();

      // Marca como não inicializado
      _isInitialized = false;

      log('Dispose dos serviços concluído');
    } catch (e) {
      log('Erro durante dispose dos serviços: $e');
    }
  }
}
