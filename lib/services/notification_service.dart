import 'dart:convert';
import 'dart:developer';
import 'dart:io' show Platform;
import 'dart:typed_data';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

Future<void> firebaseMessageBackgroundHandle(RemoteMessage message) async {
  // Background message handler
}

class NotificationService {
  static FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();
  final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  // IDs dos canais de notificação
  static const String _newOrderChannelId = 'new_order_channel';
  static const String _generalChannelId = 'general_channel';
  static bool _isLocalNotificationInitialized = false;

  void display(RemoteMessage message) async {
    try {
      // final id = DateTime.now().millisecondsSinceEpoch ~/ 1000;

      AndroidNotificationChannel channel = const AndroidNotificationChannel(
        "01",
        "Tá entregue",
        description: 'Show Tá entregue Notification',
        importance: Importance.max,
      );
      AndroidNotificationDetails notificationDetails =
          AndroidNotificationDetails(channel.id, channel.name,
              channelDescription: 'your channel Description',
              importance: Importance.high,
              priority: Priority.high,
              ticker: 'ticker');
      const DarwinNotificationDetails darwinNotificationDetails =
          DarwinNotificationDetails(
              presentAlert: true, presentBadge: true, presentSound: true);
      NotificationDetails notificationDetailsBoth = NotificationDetails(
          android: notificationDetails, iOS: darwinNotificationDetails);
      await FlutterLocalNotificationsPlugin().show(
        0,
        message.notification!.title,
        message.notification!.body,
        notificationDetailsBoth,
        payload: jsonEncode(message.data),
      );
    } catch (e) {
      // Error handling notification display
    }
  }

  void handleNotificationTap(RemoteMessage message) {}

  initInfo() async {
    try {
      await FirebaseMessaging.instance
          .setForegroundNotificationPresentationOptions(
        alert: true,
        badge: true,
        sound: true,
      );

      var request = await FirebaseMessaging.instance.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      if (request.authorizationStatus == AuthorizationStatus.authorized ||
          request.authorizationStatus == AuthorizationStatus.provisional) {
        const AndroidInitializationSettings initializationSettingsAndroid =
            AndroidInitializationSettings('@mipmap/ic_launcher');
        var iosInitializationSettings = const DarwinInitializationSettings();
        final InitializationSettings initializationSettings =
            InitializationSettings(
                android: initializationSettingsAndroid,
                iOS: iosInitializationSettings);
        await flutterLocalNotificationsPlugin.initialize(initializationSettings,
            onDidReceiveNotificationResponse: (payload) {});
        await setupInteractedMessage();
      }
    } catch (e) {
      // Error handling initialization
    }
  }

  Future<void> setupInteractedMessage() async {
    try {
      RemoteMessage? initialMessage =
          await FirebaseMessaging.instance.getInitialMessage();
      if (initialMessage != null) {
        FirebaseMessaging.onBackgroundMessage(
            (message) => firebaseMessageBackgroundHandle(message));
      }

      FirebaseMessaging.onMessage.listen((RemoteMessage message) {
        if (message.notification != null) {
          display(message);
        }
      });

      FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
        if (message.notification != null) {
          handleNotificationTap(message);
        }
      });

      if (Platform.isIOS) {
        String? apnsToken = await FirebaseMessaging.instance.getAPNSToken();
        if (apnsToken != null) {
          await FirebaseMessaging.instance.subscribeToTopic("ta_entregue");
        }
      } else {
        await FirebaseMessaging.instance.subscribeToTopic("ta_entregue");
      }
    } catch (e) {
      // Error handling setup messages
    }
  }

  static Future<String?> getToken() async {
    try {
      if (Platform.isIOS) {
        String? apnsToken = await FirebaseMessaging.instance.getAPNSToken();

        if (apnsToken == null) {
          for (int i = 0; i < 3; i++) {
            await Future.delayed(const Duration(seconds: 2));
            apnsToken = await FirebaseMessaging.instance.getAPNSToken();
            if (apnsToken != null) {
              break;
            }
          }
        }

        if (apnsToken != null) {
          String? fcmToken = await FirebaseMessaging.instance.getToken();
          return fcmToken;
        }

        return "temporary_token_for_ios";
      } else {
        return await FirebaseMessaging.instance.getToken();
      }
    } catch (e) {
      return "temporary_token_on_error";
    }
  }

  /// Inicializa notificações locais para novos pedidos
  static Future<void> initializeLocalNotifications() async {
    if (_isLocalNotificationInitialized) return;

    try {
      // Configurações para Android
      const androidSettings =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      // Configurações para iOS
      const iosSettings = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      const initSettings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      );

      await flutterLocalNotificationsPlugin.initialize(
        initSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      await _createNotificationChannels();
      _isLocalNotificationInitialized = true;

      log('Notificações locais inicializadas com sucesso');
    } catch (e) {
      log('Erro ao inicializar notificações locais: $e');
    }
  }

  /// Cria os canais de notificação
  static Future<void> _createNotificationChannels() async {
    // Canal para novos pedidos (alta prioridade com som)
    const newOrderChannel = AndroidNotificationChannel(
      _newOrderChannelId,
      'Novos Pedidos',
      description: 'Notificações de novos pedidos disponíveis',
      importance: Importance.high,
      playSound: true,
      enableVibration: true,
      enableLights: true,
      ledColor: Colors.orange,
    );

    // Canal geral (prioridade normal)
    const generalChannel = AndroidNotificationChannel(
      _generalChannelId,
      'Notificações Gerais',
      description: 'Notificações gerais do aplicativo',
      importance: Importance.defaultImportance,
      playSound: true,
      enableVibration: true,
    );

    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(newOrderChannel);

    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(generalChannel);
  }

  /// Exibe notificação para novo pedido
  static Future<void> showNewOrderNotification({
    required String orderId,
    required String storeName,
    required String storeAddress,
  }) async {
    if (!_isLocalNotificationInitialized) {
      await initializeLocalNotifications();
    }

    try {
      final androidDetails = AndroidNotificationDetails(
        _newOrderChannelId,
        'Novos Pedidos',
        channelDescription: 'Notificações de novos pedidos disponíveis',
        importance: Importance.high,
        priority: Priority.high,
        playSound: true,
        enableVibration: true,
        vibrationPattern: Int64List.fromList([0, 1000, 500, 1000]),
        enableLights: true,
        ledColor: const Color(0xFF425799),
        ledOnMs: 1000,
        ledOffMs: 500,
        icon: '@mipmap/ic_launcher',
        largeIcon: const DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
        styleInformation: BigTextStyleInformation(
          'Novo pedido disponível em $storeName\n$storeAddress',
          htmlFormatBigText: false,
          contentTitle: '🛵 Novo Pedido Disponível!',
          htmlFormatContentTitle: false,
          summaryText: 'Toque para ver detalhes',
          htmlFormatSummaryText: false,
        ),
        actions: const [
          AndroidNotificationAction(
            'view_order',
            'Ver Pedido',
            showsUserInterface: true,
          ),
          AndroidNotificationAction(
            'dismiss',
            'Dispensar',
            cancelNotification: true,
          ),
        ],
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
        categoryIdentifier: 'new_order_category',
        threadIdentifier: 'new_orders',
      );

      final notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await flutterLocalNotificationsPlugin.show(
        orderId.hashCode, // ID único baseado no ID do pedido
        '🛵 Novo Pedido Disponível!',
        'Pedido em $storeName\n$storeAddress',
        notificationDetails,
        payload: 'new_order:$orderId',
      );

      log('Notificação de novo pedido exibida: $orderId');
    } catch (e) {
      log('Erro ao exibir notificação de novo pedido: $e');
    }
  }

  /// Callback para quando notificação é tocada
  static void _onNotificationTapped(NotificationResponse response) {
    log('Notificação tocada: ${response.payload}');

    if (response.payload != null) {
      final payload = response.payload!;

      if (payload.startsWith('new_order:')) {
        final orderId = payload.substring('new_order:'.length);
        _handleNewOrderNotificationTap(orderId);
      }
    }

    // Processa ações específicas
    if (response.actionId != null) {
      switch (response.actionId) {
        case 'view_order':
          log('Ação: Ver pedido');
          break;
        case 'dismiss':
          log('Ação: Dispensar notificação');
          break;
      }
    }
  }

  /// Manipula toque em notificação de novo pedido
  static void _handleNewOrderNotificationTap(String orderId) {
    log('Usuário tocou na notificação do pedido: $orderId');
    // Aqui você pode implementar a navegação para a tela específica
  }
}
