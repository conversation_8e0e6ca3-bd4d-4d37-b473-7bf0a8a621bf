import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:emartdriver/constants.dart';
import 'package:emartdriver/firebase_options.dart';
import 'package:emartdriver/model/User.dart';
import 'package:emartdriver/services/FirebaseHelper.dart';
import 'package:emartdriver/services/ServicoMonitoramentoBloqueio.dart';
import 'package:emartdriver/services/app_initialization_service.dart';
import 'package:emartdriver/services/notification_service.dart';
import 'package:emartdriver/services/status_entregador_service.dart';
import 'package:emartdriver/ui/auth/AuthScreen.dart';
import 'package:emartdriver/ui/container/ContainerScreen.dart';
import 'package:emartdriver/ui/documents/DocumentsResubmitScreen.dart';
import 'package:emartdriver/userPrefrence.dart';
import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:firebase_auth/firebase_auth.dart' as auth;
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_foreground_task/flutter_foreground_task.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await EasyLocalization.ensureInitialized();
  await UserPreference.init();

  await _inicializarFirebaseENotificacoes();
  // Inicializar porta de comunicação do flutter_foreground_task
  FlutterForegroundTask.initCommunicationPort();

  // Inicializar serviços do app (notificações locais e background tasks)
  await AppInitializationService.initialize();

  StatusEntregadorService().initialize();

  SharedPreferences sp = await SharedPreferences.getInstance();

  runApp(
    EasyLocalization(
      supportedLocales: const [Locale('pt', 'BR')],
      path: 'assets/translations',
      fallbackLocale: Locale(sp.getString('languageCode') ?? 'pt', 'BR'),
      saveLocale: true,
      useOnlyLangCode: true,
      useFallbackTranslations: true,
      child: const MyApp(),
    ),
  );
}

BuildContext? globalContext;

Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
}

Future<void> _inicializarFirebaseENotificacoes() async {
  try {
    await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform);

    auth.FirebaseAuth.instance.setLanguageCode('pt');
    await FirebaseAppCheck.instance.activate(
      webProvider:
          ReCaptchaV3Provider('6LfKkmUrAAAAADfZuZRrmQLmRNzVv8cauwycCu0K'),
      androidProvider: AndroidProvider.playIntegrity,
      appleProvider: AppleProvider.appAttest,
    );

    await FirebaseMessaging.instance
        .setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );

    if (Platform.isIOS) {
      String? apnsToken = await FirebaseMessaging.instance.getAPNSToken();

      if (apnsToken == null) {
        await Future.delayed(const Duration(seconds: 2));
        apnsToken = await FirebaseMessaging.instance.getAPNSToken();
      }
    }

    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  } catch (e) {}
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  MyAppState createState() => MyAppState();
}

class MyAppState extends State<MyApp> with WidgetsBindingObserver {
  static User? currentUser;

  final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey(debugLabel: 'Main Navigator');

  NotificationService notificationService = NotificationService();

  @override
  Widget build(BuildContext context) {
    globalContext = context;

    return MaterialApp(
        navigatorKey: notificationService.navigatorKey,
        localizationsDelegates: context.localizationDelegates,
        supportedLocales: context.supportedLocales,
        locale: context.locale,
        title: 'Tá entregue Driver'.tr(),
        builder: EasyLoading.init(),
        theme: ThemeData(
            appBarTheme: AppBarTheme(
                centerTitle: true,
                color: Colors.transparent,
                elevation: 0,
                actionsIconTheme: IconThemeData(color: Color(COLOR_PRIMARY)),
                iconTheme: IconThemeData(color: Color(COLOR_PRIMARY)),
                systemOverlayStyle: SystemUiOverlayStyle.dark,
                toolbarTextStyle: const TextTheme(
                        titleLarge: TextStyle(
                            color: Colors.black,
                            fontSize: 17.0,
                            letterSpacing: 0,
                            fontWeight: FontWeight.w700))
                    .bodyMedium,
                titleTextStyle: const TextTheme(
                        titleLarge: TextStyle(
                            color: Colors.black,
                            fontSize: 17.0,
                            letterSpacing: 0,
                            fontWeight: FontWeight.w700))
                    .titleLarge),
            bottomSheetTheme:
                const BottomSheetThemeData(backgroundColor: Colors.white),
            primaryColor: Color(COLOR_PRIMARY),
            brightness: Brightness.light),
        debugShowCheckedModeBanner: false,
        color: Color(COLOR_PRIMARY),
        home: const OnBoarding());
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _inicializarServicosAssincrono();
    });
  }

  notificationInit() async {
    try {
      await notificationService.initInfo();
      String? token = await NotificationService.getToken();
      if (token != null) {
        if (currentUser != null) {
          await FireStoreUtils.getCurrentUser(currentUser!.userID)
              .then((value) {
            if (value != null) {
              currentUser = value;
              currentUser!.fcmToken = token;
              FireStoreUtils.updateCurrentUser(currentUser!);
            }
          });
        }
      }
    } catch (_) {}
  }

  setUpToken() async {
    if (MyAppState.currentUser != null) {
      try {
        final token = await FireStoreUtils.firebaseMessaging.getToken();
        if (token != null) {
          MyAppState.currentUser!.fcmToken = token;
          await FireStoreUtils.updateCurrentUser(currentUser!);
        }
      } catch (_) {
        // Error handled silently
      }
    }
  }

  void _inicializarServicosAssincrono() async {
    await notificationInit();
    await setUpToken();
  }
}

class OnBoarding extends StatefulWidget {
  const OnBoarding({super.key});

  @override
  State createState() {
    return OnBoardingState();
  }
}

class OnBoardingState extends State<OnBoarding> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: CircularProgressIndicator.adaptive(
          valueColor: AlwaysStoppedAnimation(
            Color(COLOR_PRIMARY),
          ),
        ),
      ),
    );
  }

  Future<Widget> determinarTelaInicial() async {
    auth.User? firebaseUser = auth.FirebaseAuth.instance.currentUser;
    if (firebaseUser == null) {
      return const AuthScreen();
    }

    User? user = await FireStoreUtils.getCurrentUser(firebaseUser.uid);
    if (user == null) {
      return const AuthScreen();
    }

    if (user.cpfCnpj != null && user.cpfCnpj != '') {
      final value = user.cpfCnpj ?? '';
      if (value.length == 11) {
        user.cpf = value;
      } else {
        user.cnpj = value;
      }
      await FireStoreUtils.updateCurrentUser(user);
    }

    if (user.role != USER_ROLE_DRIVER) {
      return const AuthScreen();
    }

    if (user.released_for_use == false) {
      return DocumentsResubmitScreen(user: user);
    }

    if (user.active) {
      user.isActive = true;
      user.role = USER_ROLE_DRIVER;
      user.fcmToken = await NotificationService.getToken() ?? '';
      await FireStoreUtils.updateCurrentUser(user);
      MyAppState.currentUser = user;

      // Inicializar monitoramento de bloqueio
      _inicializarMonitoramentoBloqueio();

      return ContainerScreen(user: user);
    } else {
      user.isActive = false;
      user.lastOnlineTimestamp = Timestamp.now();
      await FireStoreUtils.updateCurrentUser(user);
      await auth.FirebaseAuth.instance.signOut();
      MyAppState.currentUser = null;
      return const AuthScreen();
    }
  }

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _navegarParaTelaInicial();
      }
    });
  }

  void _inicializarMonitoramentoBloqueio() {
    final servicoMonitoramento = ServicoMonitoramentoBloqueio();

    servicoMonitoramento.iniciarMonitoramento(
      aoBloquear: (bloqueio) {
        if (mounted && context.mounted) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted && context.mounted) {
              servicoMonitoramento.navegarParaTelaBloqueio(context, bloqueio);
            }
          });
        }
      },
      aoDesbloquear: (bloqueio) {
        // Entregador desbloqueado
      },
    );
  }

  void _navegarParaTelaInicial() async {
    try {
      if (!mounted) return;

      Widget telaDestino = await determinarTelaInicial();

      if (!mounted) return;

      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => telaDestino),
        (route) => false,
      );
    } catch (e) {
      log(e.toString());
    }
  }
}
